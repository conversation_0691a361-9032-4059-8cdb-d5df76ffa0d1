<script setup lang="ts">
const { t } = useI18n()

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('vision.title'))
})

const visionList = ref([
	{
		title: t('vision.vision-1-title'),
		text: t('vision.vision-1-text'),
		icon: 'ui:vision-idea',
	},
	{
		title: t('vision.vision-2-title'),
		text: t('vision.vision-2-text'),
		icon: 'ui:vision-growth',
	},
])

const missionList = ref([
	{
		title: t('vision.mission-1-title'),
		text: t('vision.mission-1-text'),
		icon: 'ui:mission-quality',
	},
	{
		title: t('vision.mission-2-title'),
		text: t('vision.mission-2-text'),
		icon: 'ui:mission-satisfy',
	},
])

useSeoMeta({
	title: () => `${t('vision.meta-title')} | ${t('header.meta-site-name')}`,
	description: () => t('app.version-meta-description'),
	ogTitle: () => `${t('vision.meta-title')} | ${t('header.meta-site-name')}`,
	ogDescription: () => t('app.version-meta-description'),
	twitterTitle: () => `${t('vision.meta-title')} | ${t('header.meta-site-name')}`,
	twitterDescription: () => t('app.version-meta-description'),
})
const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)

useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>
	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="relative text-center justify-center gap-4 text-white  rounded-lg !p-0">
			<NuxtImg
				src="/images/services/lg.png"
				sizes="1765px"
				width="1765"
				height="537"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden lg:block w-full"
				alt="Large screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/services/md.png"
				sizes="991px"
				width="991"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover hidden md:block lg:hidden w-full"
				alt="Medium screen image"
				:preload="true"
			/>

			<NuxtImg
				src="/images/services/sm.png"
				sizes="398px"
				width="398"
				height="296"
				format="webp"
				quality="90"
				class="rounded-lg object-cover block md:hidden w-full"
				alt="Small screen image"
				:preload="true"
			/>
			<div class="absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0">
				<h1 class="font-bold text-2xl max-sm:text-sm">
					{{ $t('vision.title') }}
				</h1>
				<p class="text-xl font-semibold max-sm:text-xs">
					{{ $t('vision.sub-title') }}
				</p>
			</div>
		</CardHeader>
		<CardContent class="flex flex-col gap-6 p-0">
			<div class="flex justify-between items-center gap-2 py-4 px-10 max-sm:px-4">
				<div class="flex w-1/2 p-12 max-sm:w-full max-sm:hidden">
					<NuxtImg
						src="images/vision.png"
						class="max-w-md h-full w-full"
					/>
				</div>

				<div class="flex flex-col w-1/2 gap-2 max-sm:w-full">
					<span class="text-xl font-semibold">
						{{ $t('vision.vision-title') }}
					</span>
					<p class="text-base text-gray-500">
						{{ $t('vision.vision-text') }}
					</p>
					<div class="flex w-full gap-4 my-2">
						<div
							v-for="(item, index) in visionList"
							:key="`vision-${index}`"
							class="flex flex-col p-4 gap-2 rounded-lg bg-sky-50 max-sm:w-1/2"
						>
							<div class="flex justify-between items-center">
								<span class="text-primary-600 font-normal">
									{{ item.title }}
								</span>
								<Icon
									:name="item.icon"
									size="15px"
								/>
							</div>
							<p class="font-normal text-gray-600">
								{{ item.text }}
							</p>
						</div>
					</div>
				</div>
			</div>

			<div class="flex justify-between items-center gap-2 bg-sky-50 py-4 px-10 max-sm:px-4">
				<div class="flex flex-col w-1/2 gap-2 max-sm:w-full">
					<span class="text-xl font-semibold">
						{{ $t('vision.mission-title') }}
					</span>
					<p class="text-base text-gray-500">
						{{ $t('vision.mission-text') }}
					</p>
					<div class="flex w-full gap-4 my-2">
						<div
							v-for="(item, index) in missionList"
							:key="`mission-${index}`"
							class="flex flex-col p-4 gap-2 rounded-lg bg-sky-100 max-sm:w-1/2"
						>
							<div class="flex justify-between items-center">
								<span class="text-primary-600 font-normal">
									{{ item.title }}
								</span>
								<Icon
									:name="item.icon"
									size="15px"
								/>
							</div>
							<p class="font-normal text-gray-600">
								{{ item.text }}
							</p>
						</div>
					</div>
				</div>
				<div class="flex w-1/2 p-12 justify-end max-sm:hidden">
					<NuxtImg
						src="images/mission.png"
						class="max-w-md h-full w-full"
					/>
				</div>
			</div>
		</CardContent>
	</Card>
</template>
