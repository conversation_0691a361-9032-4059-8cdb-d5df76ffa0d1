import Redis from 'ioredis'
// Alternative: import { createRedisStorage } from '~/server/utils/redis'

export default defineEventHandler(async () => {
	try {
		// Use regular Redis connection instead of cluster since AWS ElastiCache is likely a single instance
		const redis = new Redis({
			host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
			port: 6379,
			tls: {
				servername: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
			},
		})

		console.log('Connected to Redis instance')

		const keys: string[] = []

		// Scan the single Redis instance directly
		console.log('Scanning Redis instance...')
		let cursor = '0'
		do {
			const [nextCursor, foundKeys] = await redis.scan(cursor, 'MATCH', '*', 'COUNT', 100)
			cursor = nextCursor
			console.log(`Scan result: cursor=${nextCursor}, foundKeys=${foundKeys.length}`)

			if (foundKeys.length > 0) {
				console.log(`Found keys:`, foundKeys)
				keys.push(...foundKeys)
			}
		} while (cursor !== '0')

		console.log(`Total keys found: ${keys.length}`)

		// Close the connection
		await redis.quit()

		return {
			message: 'Cache keys',
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error: `${error}`,
		}
	}
})
