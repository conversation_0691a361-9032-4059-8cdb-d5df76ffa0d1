<script setup lang="ts">
import type { Order } from '~/interfaces/auth/order'

const { priceFormat } = useCurrency()

const { order, loading } = defineProps<{
	order?: Order
	loading?: boolean
}>()

/** Prepare Phone Number **/
const reviverPhoneNumber = computed<string>(() => {
	return [
		'+',
		order.user?.phone?.code,
		order.user?.phone?.number,
	].join('')
})

/** Prepare Address Description **/
const reviverAddress = computed<string>(() => {
	return [
		order?.address?.fullAddress,
		order?.address?.district,
		order?.address?.street,
		order?.address?.buildingNumber,
		order?.address?.apartmentNumber,
	].filter(Boolean).join(', ')
})
</script>

<template>
	<Card>
		<CardHeader>
			<template v-if="loading">
				<Skeleton class="w-1/2 h-6" />
			</template>
			<template v-else>
				<span class="text-xl font-bold">{{ $t('orders.details') }}</span>
			</template>
		</CardHeader>

		<CardContent class="flex flex-col gap-4 pb-4">
			<template v-if="loading">
				<div class="flex flex-col w-full gap-4">
					<div class="flex justify-between gap-4">
						<Skeleton class="h-10 w-1/2" />
						<Skeleton class="h-10 w-1/2" />
					</div>
					<div class="flex justify-between gap-4">
						<Skeleton class="h-10 w-1/2" />
						<Skeleton class="h-10 w-1/2" />
					</div>
					<div class="flex justify-between gap-4">
						<Skeleton class="h-10 w-1/2" />
						<Skeleton class="h-10 w-1/2" />
					</div>

					<Skeleton class="h-10 w-full" />
					<Skeleton class="h-10 w-full" />
					<Skeleton class="h-10 w-full" />

					<div class="flex self-end flex-col gap-4 sm:w-1/2 xs:w-full">
						<Skeleton class="h-10 w-full" />
						<Skeleton class="h-10 w-full" />
						<Skeleton class="h-10 w-full" />
					</div>
				</div>
			</template>
			<template v-else>
				<div class="grid grid-cols-2 gap-4 w-full pb-4 max-sm:grid-cols-1">
					<div class="flex items-center gap-2">
						<span class="w-36 text-gray-600 font-medium">{{ $t('orders.number') }}</span>
						<span class="text-gray-700">{{ order.orderId }}</span>
					</div>

					<div class="flex items-center gap-2">
						<span class="w-36 text-gray-600 font-medium">{{ $t('form.receiver-name') }}</span>
						<span class="text-gray-700">{{ order.user?.fullName }}</span>
					</div>

					<div class="flex items-center gap-2">
						<span class="w-36 text-gray-600 font-medium">{{ $t('orders.date') }}</span>
						<span class="text-gray-700">
							{{ useDateFormat(order?.createdAt, 'DD-MM-YYYY hh:mm A') }}
						</span>
					</div>

					<div class="flex items-center gap-2">
						<span class="w-36 text-gray-600 font-medium">{{ $t('form.reviver-phone-number') }}</span>
						<span
							class="text-gray-700"
							dir="ltr"
						>
							{{ reviverPhoneNumber }}
						</span>
					</div>

					<div class="flex items-center gap-2">
						<span class="w-36 text-gray-600 font-medium">{{ $t('orders.number') }}</span>
						<span class="text-gray-700">{{ order.orderId }}</span>
					</div>

					<div class="flex items-center gap-2">
						<span class="w-36 text-gray-600 font-medium">{{ $t('form.address') }}</span>
						<span class="text-gray-700">{{ reviverAddress }}</span>
					</div>
				</div>

				<div class="flex w-full border border-gray-100 my-2" />
				<div class="flex flex-col gap-4 w-full items-center">
					<div class="flex w-full font-semibold text-gray-700 -mb-2 text-lg max-sm:text-base">
						<span class="flex flex-grow">{{ $t('orders.product') }}</span>
						<span class="w-24 text-center">{{ $t('product.quantity') }}</span>
						<span class="w-20 text-center">{{ $t('product.price') }}</span>
						<span class="w-20 text-center">{{ $t('orders.total') }}</span>
					</div>
					<NuxtLinkLocale
						v-for="item in order.orderItems"
						:key="`${item.productOrderItemId}`"
						class="flex w-full border border-gray-200 rounded-lg p-px items-center text-base max-sm:text-sm"
						:to="`/product/${item.product?.slug}`"
					>
						<div class="flex w-44 h-28 bg-gray-100 justify-center items-center rounded-s-lg max-sm:hidden">
							<NuxtImg
								:src="item.product.media?.cover?.[0]?.src"
								class="object-contain h-full p-4"
								provider="backend"
							/>
						</div>
						<div class="flex h-full flex-grow p-4 items-center">
							<span class="px-2 font-medium text-gray-700">{{ item.product?.name }}</span>
						</div>

						<div class="flex w-20 justify-center">
							{{ item.quantity }}
						</div>

						<div class="flex w-20 justify-center">
							<span class="font-bold">
								{{ priceFormat(item.price?.value) }}
							</span>
						</div>

						<div class="flex w-20 justify-center">
							<span class="font-bold">
								{{ priceFormat(item.quantity * item.price?.value) }}
							</span>
						</div>
					</NuxtLinkLocale>
				</div>
				<div class="flex w-full border border-gray-100 my-2" />
				<div class="flex w-full justify-end max-sm:justify-between">
					<div class="flex w-1/2 flex-col gap-2 max-sm:w-full">
						<div class="flex items-center w-full justify-between">
							<span class="text-base font-normal text-gray-600">
								{{
									$t('cart.payment-sub-amount-title', {
										item: $t('cart.item', { count: order?.orderItems?.length }),
										number: order?.orderItems?.length,
									})
								}}
							</span>

							<span class="text-base font-normal text-gray-600">
								{{ priceFormat(order.subTotal.value) }}
							</span>
						</div>
						<div class="flex items-center w-full justify-between">
							<span class="text-base font-normal text-gray-600">
								{{ $t('orders.delivery-service') }}
							</span>

							<span class="text-base font-normal text-gray-600">
								{{ priceFormat(order.shippingPrice?.value) }}
							</span>
						</div>
						<div class="flex items-center w-full justify-between hidden">
							<span class="text-base font-normal text-gray-600">
								{{ $t('orders.coupon-discount') }}
							</span>

							<span class="text-base font-normal text-gray-600">
								N/A
							</span>
						</div>
						<div class="flex border-t border-dashed border-gray-200 w-full my-2" />
						<div class="flex items-center w-full justify-between">
							<span class="text-xl font-bold text-gray-600">
								{{ $t('orders.total') }}
							</span>

							<span class="text-xl font-bold text-gray-600">
								{{ priceFormat(order.total?.value) }}
							</span>
						</div>
					</div>
				</div>
			</template>
		</CardContent>
	</Card>
</template>
