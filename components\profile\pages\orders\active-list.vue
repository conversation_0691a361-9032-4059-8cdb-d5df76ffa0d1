<script setup lang="ts">
import { useCurrency } from '~/composables/useCurrency'
import type { Order } from '~/interfaces/auth/order'

const { priceFormat } = useCurrency()
const { orders } = defineProps<{
	orders?: Order[]
}>()

const list = computed(() => orders.filter(order => order.status))
const isEmptyList = computed(() => !list.value?.length)
const expandId = ref(null)

const openTrackingOrder = (orderId: number): void => {
	const localePath = useLocalePath()
	navigateTo(localePath(`/my/orders/${orderId}/tracking`))
}
</script>

<template>
	<CardContent>
		<div class="w-full h-full">
			<template v-if="isEmptyList">
				<div class="flex flex-col w-full h-full items-center gap-6 py-14">
					<Icon
						name="ui:empty-orders"
						class="w-full h-60"
					/>

					<span class="text-lg max-w-sm text-center font-semibold">
						{{ $t('orders.empty-text') }}
					</span>

					<Button>
						{{ $t('orders.empty-text-btn') }}
					</Button>
				</div>
			</template>
			<template v-else>
				<div
					v-for="order in list"
					:key="order.orderId"
					class="flex w-full rounded-lg flex-col border cursor-pointer my-4"
					:class="{ active: expandId === order.orderId }"
					@click="expandId = order.orderId"
				>
					<div
						class="header flex w-full justify-between items-center gap-4 text-base font-semibold p-4 max-sm:items-start max-sm:bg-sky-50 max-sm:flex-col-reverse"
					>
						<div class="flex sm:items-center sm:justify-between gap-6 max-sm:flex-col">
							<div class="flex items-center gap-2">
								<span class="text-gray-600">{{ $t('orders.number') }}: </span>
								<span class="text-primary-600">{{ order?.orderId }}</span>
							</div>
							<div class="flex items-center gap-2">
								<span class="text-gray-600">{{ $t('orders.date') }}: </span>
								<span class="text-primary-600">{{ useDateFormat(order?.createdAt, 'DD-MM-YYYY hh:mm A') }}</span>
							</div>
							<div class="flex items-center gap-2">
								<span class="text-gray-600">{{ $t('orders.total-amount') }}: </span>
								<span class="text-primary-600">{{ priceFormat(order.total.value) }}</span>
							</div>
						</div>

						<StatusOrder :status="order.status" />
					</div>
					<div class="children flex flex-col gap-4">
						<template
							v-for="item in order.orderItems"
							:key="item.orderId"
						>
							<div class="flex w-full gap-4 items-center">
								<div class="flex w-20 h-14 items-center justify-center bg-gray-100 p-2 rounded shadow">
									<NuxtImg
										:src="item.product?.media?.cover?.[0]?.src"
										class="object-contain max-h-14 p-2"
										provider="backend"
									/>
								</div>

								<div class="flex w-full gap-2">
									<span class="text-base font-normal">{{ item.product?.name }}</span>
								</div>
							</div>
						</template>
						<div class="flex w-full justify-end">
							<Button
								:size="'sm'"
								@click.prevent="openTrackingOrder(order.orderId)"
							>
								{{ $t('orders.track') }}
							</Button>
						</div>
					</div>
				</div>
			</template>
		</div>
	</CardContent>
</template>

<style scoped lang="scss">
.children {
  @apply h-0 overflow-hidden transition-all ease-in-out duration-100;
}

.active {
  @apply shadow-md;
  .header {
    @apply border-b;
  }

  .children {
    @apply p-4 overflow-hidden h-auto !important;
  }
}
</style>
