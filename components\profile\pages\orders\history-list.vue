<script setup lang="ts">
import { useCurrency } from '~/composables/useCurrency'
import type { Order, OrderItem } from '~/interfaces/auth/order'

const { priceFormat } = useCurrency()
const { orders } = defineProps<{
	orders?: Order[]
}>()

/** Prepare order items array of Object **/
const items = computed<OrderItem[]>(() => {
	const list = orders
		.map((order) => {
			const orderItems = order.orderItems.map((item) => {
				return {
					...item,
					order,
				} as OrderItem
			}) as OrderItem[]

			return {
				...orderItems,
				orderItems,
			} as Order
		})

	return list
		.flatMap(order => order.orderItems) as OrderItem[]
})

const isEmptyList = computed(() => !items.value?.length)

const onOpenDetails = (orderId: number): void => {
	const localePath = useLocalePath()
	navigateTo(localePath(`/my/orders/${orderId}/details`))
}
</script>

<template>
	<CardContent>
		<div class="flex flex-col w-full h-full gap-4 py-4">
			<template v-if="isEmptyList">
				<div class="flex flex-col w-full h-full items-center gap-6 py-14">
					<Icon
						name="ui:empty-orders-history"
						class="w-full h-60"
					/>

					<span class="text-lg max-w-sm text-center font-semibold">
						{{ $t('orders.empty-text') }}
					</span>

					<Button>
						{{ $t('orders.empty-text-btn') }}
					</Button>
				</div>
			</template>
			<template v-else>
				<div
					v-for="item in items"
					:key="item.productId"
					class="flex w-full rounded-lg flex-col border overflow-hidden"
				>
					<div class="flex w-full items-center h-44 text-base max-sm:text-sm max-sm:h-auto max-sm:p-2">
						<div class="flex w-52 h-44 items-center justify-center bg-gray-100 p-2 max-sm:hidden">
							<NuxtImg
								:src="item.product?.media?.cover?.[0]?.src"
								class="object-contain h-full p-4"
								provider="backend"
							/>
						</div>

						<div class="flex flex-col flex-grow gap-2 px-4 max-sm:px-2 max-sm:gap-0">
							<div class="flex items-start gap-2 justify-between">
								<div class="text-sm truncate-2-line font-semibold h-10 max-w-sm leading-snug">
									{{ item?.product?.name }}
								</div>
								<StatusOrder
									:status="item.order.status"
								/>
							</div>

							<div class="flex gap-2 items-center text-gray-600 font-semibold">
								<span class=" w-28">{{ $t('orders.date') }}</span>
								<span
									class=" px-2"
									dir="ltr"
								>
									{{ useDateFormat(item?.order?.createdAt, 'DD-MM-YYYY hh:mm A') }}
								</span>
							</div>

							<div class="flex gap-2 items-center text-gray-600 font-semibold">
								<span class="w-28">{{ $t('orders.number') }}</span>
								<span
									class="px-2"
									dir="ltr"
								>
									{{ item.orderId }}
								</span>
							</div>

							<div class="flex w-full gap-2 justify-between text-gray-600">
								<div class="flex gap-2 items-center">
									<span class="w-28 font-semibold">{{ $t('orders.total-amount') }}</span>
									<span class="px-2 font-bold">
										{{ priceFormat(item.price.value) }}
									</span>
								</div>

								<Button
									variant="text"
									:size="'sm'"
									@click.prevent="onOpenDetails(item.orderId)"
								>
									{{ $t('orders.details') }}
								</Button>
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
	</CardContent>
</template>
