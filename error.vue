<script setup lang="ts">
import type { NuxtError } from '#app'

const { error } = defineProps({
	error: Object as () => NuxtError,
})

const isNotFound = computed<boolean>(() => error?.statusCode === 404)

const { t } = useI18n()
useSeoMeta({
	title: () => `Error Page ${error?.statusCode} |  ${t('header.meta-site-name')}`,
	ogTitle: () => `Error Page ${error?.statusCode} |  ${t('header.meta-site-name')}`,
	twitterTitle: () => `Error Page ${error?.statusCode} |  ${t('header.meta-site-name')}`,
})

onMounted(() => {
	console.log('========= Error Page =========')
	console.log(error)
	if (error.stack) {
		const stackTrace = error.stack
		const regex = /at (.+?) \((.+):(\d+):(\d+)\)/g
		const matches = [...stackTrace.matchAll(regex)]
		const result = matches.map(([, functionName, filePath, line, column]) => ({
			functionName,
			filePath,
			line: Number(line),
			column: Number(column),
		}))

		console.log(result)
	}

	console.log('========= /Error Page =========')
})
</script>

<template>
	<NuxtLayout>
		<div class="flex flex-col gap-4 items-center justify-center py-28 bg-white text-primary-700 px-4 rounded-lg mt-12">
			<Icon
				name="ui:404"
				size="320px"
				class="text-primary-600"
			/>
			<span class="text-4xl font-bold">
				{{ error?.statusCode }}
			</span>
			<div class="flex gap-4 flex-col items-center">
				<p class="text-base my-4 max-w-[800px] text-center font-bold text-gray-600">
					<template v-if="isNotFound">
						{{ $t('error.404-title') }}
					</template>
					<template v-else>
						{{ $t('error.message') }}
					</template>
				</p>
				<NuxtLinkLocale
					to="/"
					class="mt-6 px-6 py-3 text-sm font-semibold text-white bg-primary-700 rounded-lg shadow-md hover:bg-primary-600 transition-all duration-300"
				>
					{{ $t('error.back-btn-title') }}
				</NuxtLinkLocale>
			</div>
		</div>
		<div
			v-if="false"
			class="flex flex-col gap-4 items-center justify-center min-h-screen bg-primary-100 text-primary-700 px-4"
		>
			<template v-if="!isNotFound">
				<Icon
					name="ui:404"
					size="90px"
					class="text-primary-600"
				/>
				<div
					class="flex flex-col items-center"
				>
					<h1 class="text-8xl font-normal">
						{{ error?.statusCode || $t('error.page-title') }}
					</h1>
					<p class="text-xl mt-4">
						{{ error?.statusMessage || 'Something went wrong' }}
					</p>

					<p class="text-sm mt-4 max-w-lg text-center">
						{{ $t('error.message') }}
					</p>
					<NuxtLinkLocale
						to="/"
						class="mt-6 px-6 py-3 text-lg font-semibold text-white bg-primary-700 rounded-lg shadow-md hover:bg-primary-600 transition-all duration-300"
					>
						{{ $t('error.back-btn-title') }}
					</NuxtLinkLocale>
				</div>
			</template>
			<template v-else>
				<ErrorNotFound />
			</template>
		</div>
	</NuxtLayout>
</template>
