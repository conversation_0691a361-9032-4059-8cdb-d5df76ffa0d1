<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type { AuthPhone } from '~/interfaces/auth/form'
import type FormPhoneValue from '~/interfaces/form'

const { t } = useI18n()
const key = ref(1)

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('contact.title'))
})

interface Item {
	icon?: string
	name?: string
	text?: string
	value?: string
	bg?: string
	color?: string
	link?: string
}

const list = ref<Item[]>([
	{
		icon: 'ui:email',
		name: t('contact.email'),
		text: t('contact.email-note'),
		value: '<EMAIL>',
		bg: '#DBEAFE',
		color: '#2563EB',
		link: 'mailto:<EMAIL>',
	},
	{
		icon: 'ui:phone',
		name: t('contact.phone'),
		text: t('contact.phone-note'),
		value: '+962-791009595',
		bg: '#F3F9FC',
		color: '#893477',
		link: 'tel:+962-791009595',
	},
	{
		icon: 'ui:whatsapp',
		name: t('contact.whatsapp'),
		text: t('contact.whatsapp-note'),
		value: '+962-791009595',
		bg: '#D4EDD1',
		color: '#29A71A',
		link: 'https://wa.me/962791009595',
	},
])

/** Form Body **/
const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		fullName: z.string().min(1, t('error.required')),
		email: z.string().email(t('error.email')),
		problemType: z.string().min(1, t('error.required')),
		message: z.string().min(1, t('error.required')),
		phone: z.object({
			number: z.string().min(5, t('error.required')),
			iso: z.string().min(1, t('error.required')),
			code: z.string().min(1, t('error.required')),
			isValid: z.boolean(),
		})
			.refine((phone) => {
				if (!phone.isValid) {
					return Form.setErrors({
						phone: t('error.phone-number-invalid'),
					})
				}

				return true
			}, {
				message: t('error.phone-number-invalid'),
			}),
	})),

	initialValues: {
		fullName: '',
		email: '',
		problemType: '',
		phone: {
			number: '',
			code: '',
			iso: '',
			isValid: false,
		},
		message: '',
	},
})

const onPhoneUpdate = (value: FormPhoneValue) => {
	Form.setFieldValue('phone', {
		number: value.nationalNumber,
		code: value.countryCallingCode,
		iso: value.countryCode,
		isValid: value.isValid,
	})
}

const onSubmit = Form.handleSubmit((values: AuthPhone): Promise<void> => {
	const { $api } = useNuxtApp()
	return $api<never>('/contact-us', {
		method: 'POST',
		body: values,
	}).then(() => {
		toast.success(t('form.contact-saved-successfully'))
		Form.resetForm()
		key.value += 1
	}).catch((err) => {
		console.log(err)
	})
})

useSeoMeta({
	title: () => `${t('contact.meta-title')} | ${t('header.meta-site-name')}`,
	description: () => t('contact.meta-description'),
	ogTitle: () => `${t('contact.meta-title')} | ${t('header.meta-site-name')}`,
	ogDescription: () => t('contact.meta-description'),
	twitterTitle: () => `${t('contact.meta-title')} | ${t('header.meta-site-name')}`,
	twitterDescription: () => t('contact.meta-description'),
})

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)
useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>

	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="text-center justify-center gap-4 rounded-lg">
			<h1 class="font-bold text-2xl">
				{{ $t('contact.title') }}
			</h1>
			<h2 class="text-lg text-gray-600">
				{{ $t('contact.sub-title') }}
			</h2>
		</CardHeader>
		<CardContent class="grid grid-cols-2 gap-6 py-12 max-md:grid-cols-1">
			<form
				:key="`form-${key}`"
				class="flex col-span-1"
			>
				<div class="flex flex-col gap-4 w-full rounded-lg border shadow p-4 justify-center">
					<span class="whitespace-pre-wrap text-center">
						{{ $t('contact.form-title') }}
					</span>

					<FormField
						v-slot="{ componentField }"
						name="fullName"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-bold">
								{{ $t('form.full-name') }}*
							</FormLabel>
							<FormControl>
								<Input
									type="text"
									:placeholder="$t('form.full-name')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<FormField
						v-slot="{ componentField }"
						name="email"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-bold">
								{{ $t('form.email') }}*
							</FormLabel>
							<FormControl>
								<Input
									type="email"
									:placeholder="$t('form.email')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<FormPhone
						:error="Form.errors?.value?.phone"
						@update="onPhoneUpdate"
					/>

					<FormField
						v-slot="{ componentField }"
						name="problemType"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-bold">
								{{ $t('form.problem-type') }}*
							</FormLabel>
							<FormControl>
								<Input
									type="text"
									:placeholder="$t('form.problem-type')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<FormField
						v-slot="{ componentField }"
						name="message"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-bold">
								{{ $t('form.message') }}*
							</FormLabel>
							<FormControl>
								<textarea
									class="flex border w-full min-h-20 p-2 rounded outline-none text-sm"
									:placeholder="$t('form.message')"
									rows="7"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<Button @click="onSubmit">
						<span>{{ $t('form.send-message') }}</span>
					</Button>
				</div>
			</form>
			<div class="flex col-span-1">
				<div class="flex justify-between items-start gap-4 w-full max-sm:flex-col">
					<div
						v-for="(item, index) in list"
						:key="`info-${index}`"
						class="flex flex-col gap-2 border rounded-lg shadow w-1/3 text-xs justify-center items-center py-4 max-sm:w-full"
					>
						<div
							class="flex flex-col p-2 w-10 h-10 items-center justify-center rounded-full"
							:style="{ background: item.bg }"
						>
							<Icon
								:name="item.icon"
								size="20px"
							/>
						</div>
						<span>{{ item.name }}</span>
						<span>{{ item.text }}</span>
						<a
							dir="ltr"
							:style="{ color: item.color }"
							:href="item.link"
						>
							{{ item.value }}
						</a>
					</div>
				</div>
			</div>
		</CardContent>
	</Card>
</template>
