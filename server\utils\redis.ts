import { createStorage } from 'unstorage'
import redisDriver from 'unstorage/drivers/redis'

/**
 * Creates a Redis storage instance using unstorage
 * This provides a consistent way to connect to Redis across the application
 */
export function createRedisStorage() {
	return createStorage({
		driver: redisDriver({
			base: '{unstorage}',
			// Use single Redis instance configuration instead of cluster
			host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
			port: 6379,
			tls: {
				servername: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
			},
		}),
	})
}
