<script setup lang="ts">
import type { UseProductDetails } from '~/interfaces/product/details'
import type { Wishlist } from '~/interfaces/wishlist/list'
import { useCartStore } from '~/store/useCartStore'

interface Payload {
	productId?: number
	quantity?: number
	bundleId?: undefined
	varianceId?: number
}

const { product } = defineProps<{ product: Wishlist }>()
const emit = defineEmits<{
	(event: 'remove:wish-list', productId: number): void
	(event: 'add:cart', payload: Payload): void
}>()

const item = ref<UseProductDetails | null>(null)

onMounted(() => {
	item.value = useProduct(product) as UseProductDetails
})

const cartStore = useCartStore()
const media = computed(() => (item.value as UseProductDetails)?.media)
const image = computed(() => media.value?.cover?.[0]?.src || media.value?.gallery?.[0]?.src)
const hasRemove = ref(false)

const onRemove = () => {
	hasRemove.value = true
	nextTick(() => emit('remove:wish-list', item.value.productId))
}

const hasCart = computed(() => cartStore.hasCart(item.value.productId))
</script>

<template>
	<div
		class="flex w-full border border-gray-200 rounded-lg overflow-hidden transition-all ease-in-out duration-300"
		:class="{ hidden: hasRemove }"
	>
		<div
			v-if="item"
			class="grid grid-cols-3 gap-1 h-36"
		>
			<NuxtLinkLocale
				:to="`/product/${item.slug}`"
				class="flex col-span-1 bg-gray-100 flex-col justify-center items-center"
			>
				<NuxtImg
					:src="image"
					:alt="item.name"
					provider="backend"
					class="object-contain max-h-[80%] w-36"
				/>
			</NuxtLinkLocale>
			<div class="flex flex-col col-span-2 justify-evenly px-2 h-full overflow-hidden">
				<div class="flex w-full items-center justify-between">
					<NuxtLinkLocale
						:to="`/product/${item.slug}`"
						class="truncate-2-line text-sm font-semibold"
					>
						{{ item.name }}
					</NuxtLinkLocale>
					<button
						class="flex items-center"
						@click="onRemove"
					>
						<Icon
							name="lucide:trash-2"
							size="20px"
							class="text-gray-400"
						/>
					</button>
				</div>
				<NuxtLinkLocale
					class="flex w-full"
					:to="`/product/${item.slug}`"
				>
					<span class="text-green-600 text-lg font-bold">{{ item.priceFormatted }}</span>
				</NuxtLinkLocale>

				<div class="flex">
					<Button
						v-if="!hasCart"
						:size="'sm'"
						@click="emit('add:cart', {
							productId: product.productId,
							quantity: 1,
							bundleId: null,
							varianceId: product.variance.varianceId,
						})"
					>
						<span class="text-xs p-2">{{ $t('wish-list.add-to-cart') }}</span>
					</Button>
				</div>
			</div>
		</div>
	</div>
</template>
