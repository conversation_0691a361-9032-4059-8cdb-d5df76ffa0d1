<script setup lang="ts">
import type { CheckoutOrder } from '~/interfaces/checkout/order'
import type { CheckoutPayments } from '~/interfaces/checkout/payments'
import { useAuthStore } from '~/store/useAuthStore.client'

const { order } = defineProps<{
	order?: CheckoutOrder
}>()

const emit = defineEmits<{
	(event: 'set:flow', value: number): void
}>()

const { data, error, status } = await useApi<CheckoutPayments[]>(`/orders/${order?.orderId}/payment-methods`)

if (error.value) {
	console.log('error on fetching shipping list', error.value)
}

const authStore = useAuthStore()
const paymentList = computed(() => data?.value as CheckoutPayments[])
const loading = computed<boolean>(() => status?.value !== 'success')
const selectedId = ref<number | null>(order?.paymentMethodId || null)
const isSubmitting = ref<boolean>(false)
const isLocked = computed(() => order?.status !== 'draft')
const isLoggedIn = computed(() => !!authStore.isLoggedIn)

/** on select payment **/
const onSelectPayment = async (paymentId: number) => {
	selectedId.value = paymentId
	isSubmitting.value = true
	const { $api } = useNuxtApp()
	return $api<unknown>(`/orders/${order.orderId}/set-payment`, {
		method: 'POST',
		body: {
			paymentMethodId: selectedId.value,
		},
	})
		.finally(() => {
			isSubmitting.value = false
		})
}

const nextFlow = async () => {
	return emit('set:flow', 4)
}

const route = useRoute()
const loginPath = computed(() => `${route.path}?auth=login`)
</script>

<template>
	<CardContent class="flex-grow p-4">
		<div class="grid grid-cols-2 w-full gap-6 max-md:grid-cols-1">
			<template v-if="loading">
				<div
					v-for="(_, index) in Array(4)"
					:key="index"
					class="flex w-full max-w-full border rounded-lg p-2 gap-2 flex-col"
				>
					<Skeleton class="w-full h-4" />
					<Skeleton class="w-full h-7" />
				</div>
			</template>
			<template
				v-for="item in paymentList"
				v-else
				:key="item.shippingCarrierId"
			>
				<div class="flex w-full flex-col gap-2">
					<button
						:disabled="isLocked || item.module === 'wallet' && !isLoggedIn"
						class="flex flex-col border rounded-lg gap-2 cursor-pointer text-start disabled:cursor-not-allowed shadow-sm"
						:class="{ 'active': item.paymentMethodId === selectedId, 'bg-gray-200 opacity-50': item.module === 'wallet' && !isLoggedIn }"
						@click="onSelectPayment(item.paymentMethodId)"
					>
						<div class="flex gap-2 border-b py-2 px-3 items-center">
							<div class="check flex rounded-full w-5 h-5 border border-gray-300 p-0.5">
								<div class="child flex w-full h-full rounded-full" />
							</div>

							<div class="font-bold text-base flex-grow">
								{{ item.name }}
							</div>

							<div class="flex w-20 h-5 justify-end">
								<NuxtImg
									provider="backend"
									:src="item?.media?.logo?.src"
								/>
							</div>
						</div>
						<div class="flex w-full items-center text-sm gap-4 px-4 pb-2">
							<span
								v-if="item.module"
								class="font-semibold text-gray-700"
							>
								{{ $t(`checkout.payment-method-${item.module}`) }}:
							</span>
						</div>
					</button>
					<div
						v-if="item.module === 'wallet' && !isLoggedIn"
						class="flex w-full gap-2 items-center"
					>
						<Icon
							name="ui:warning"
							size="18px"
						/>
						<i18n-t keypath="wallet.auth-user-note">
							<template #login>
								<NuxtLink
									:to="loginPath"
									class="text-primary-600 underline px-0.5"
								>
									{{ $t('auth.log-in') }}
								</NuxtLink>
							</template>
						</i18n-t>
					</div>
				</div>
			</template>
		</div>
	</CardContent>
	<CardFooter class="gap-4 justify-end">
		<Button
			variant="outline"
			class="sm:min-w-24 xs:min-w-1/2"
			@click.prevent="() => emit('set:flow', 2)"
		>
			{{ $t("form.prev") }}
		</Button>

		<Button
			:disabled="!selectedId"
			class="sm:min-w-24 xs:min-w-1/2"
			:loading="isSubmitting"
			@click.prevent="() => nextFlow()"
		>
			{{ $t("form.next") }}
		</Button>
	</CardFooter>
</template>

<style scoped lang="scss">
.active {
  @apply bg-primary-300/30 border-primary-500;
  .check {
    @apply border-primary-600;
    .child {
      @apply bg-primary-600
    }
  }
}
</style>
