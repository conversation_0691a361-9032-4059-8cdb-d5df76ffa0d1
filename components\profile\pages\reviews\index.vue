<script setup lang="ts">
import { toast } from 'vue-sonner'
import ReviewCard from './card.vue'
import Paginate from '~/components/ui/pagination/Paginate.vue'
import type { Rating, Items, Pagination } from '~/interfaces/auth/rating'

const reFetchKey = ref(1)
const page = ref(1)
const reviews = ref<Items[]>([])

const { data, error, status } = await useApi<Rating>('/my/ratings', {
	query: {
		page,
		perPage: 10,
	},
	watch: [reFetchKey, page],
})

watch(() => data, (result) => {
	reviews.value = (result.value as Rating)?.items as Items[]
}, { immediate: true, deep: true })
const { t } = useI18n()
const pagination = computed<Pagination>(() => (data.value as Rating)?.pagination as Pagination)
const loading = computed(() => status.value !== 'success')
const selectedReview = ref<Items | null>(null)
const isReviewing = ref<boolean>(false)

const isEmptyList = computed<boolean>(() => {
	if (!pagination.value?.lastPage) {
		return false
	}

	return pagination.value.lastPage <= 1
})

if (error.value) {
	console.log('error on fetch ratings', error.value)
}

const selectedRateValue = computed<number>(() => selectedReview.value?.rating ?? 0)

const updateRate = (index: number) => {
	if (!selectedReview.value) {
		return
	}

	selectedReview.value.rating = 1 + index
}

const submitReview = async () => {
	isReviewing.value = true
	const { ratingId, review, productId, rating } = selectedReview.value as Items
	const { $api } = useNuxtApp()
	return $api<never>(`/products/${productId}/rating/${ratingId}`, {
		method: 'PUT',
		body: {
			review: review,
			rating: rating,
		},
	})
		.then(() => {
			toast.success(t('form.submit-rate-success'))
			selectedReview.value = null
			reFetchKey.value += 1
		})
		.catch((error) => {
			throw error
		})
		.finally(() => {
			nextTick(() => isReviewing.value = false)
		})
}
</script>

<template>
	<Card class="w-full min-h-full">
		<CardHeader class="max-sm:hidden">
			<span class="font-bold text-xl">{{ $t('profile.link-reviews-title') }}</span>
		</CardHeader>

		<CardContent class="flex flex-col flex-1 flex-grow">
			<template v-if="loading">
				<div class="flex flex-col gap-4 w-full max-sm:mt-6">
					<ReviewCard
						v-for="(_, index) in Array(2)"
						:key="`loading-${index}`"
						:loading="true"
					/>
				</div>
			</template>
			<template v-else-if="isEmptyList">
				<div class="flex flex-col  w-full justify-center items-center gap-6 py-8">
					<Icon
						name="ui:empty-reviews"
						class="w-72 h-72"
					/>

					<span class="text-lg font-bold max-w-80 text-center">
						{{ $t('reviews.empty-text') }}
					</span>

					<Button>
						<NuxtLinkLocale
							to="/"
							class="text-sm font-bold max-w-80"
						>
							{{ $t('reviews.empty-btn') }}
						</NuxtLinkLocale>
					</Button>
				</div>
			</template>
			<template v-else>
				<div class="flex flex-col gap-4 w-full max-sm:mt-6">
					<template
						v-for="review in reviews"
						:key="review.reviewId"
					>
						<ReviewCard
							:review="review"
							@edit:review="review => selectedReview={ ...review }"
						/>
					</template>
				</div>
			</template>
		</CardContent>
		<CardFooter v-if="!loading && !isEmptyList">
			<Paginate
				:items-per-page="pagination?.perPage"
				:total="pagination?.lastPage"
				:sibling-count="1"
				:show-edges="true"
				:default-page="pagination.page"
				@update:page="(p) => page = p"
			/>
		</CardFooter>
	</Card>
	<Modal
		v-if="selectedReview"
		:title="$t('form.review-edit-title')"
		size="max-w-2xl"
		@close="selectedReview = null"
	>
		<template #body>
			<div class="p-4 w-full flex flex-col gap-4">
				<div class="flex w-full gap-2">
					<span class="text-sm font-medium text-gray-500">{{ $t('form.rates') }}:</span>
					<div class="flex w-full gap-2">
						<button
							v-for="(_, index) in Array(5)"
							:key="`form-rating-${index}`"
							:class="selectedRateValue >= 1 + index ? 'text-rating-200' : 'text-rating-100'"
							@click.prevent="() => updateRate(index)"
						>
							<Icon
								name="ui:rate-star"
							/>
						</button>
					</div>
				</div>

				<textarea
					v-model="selectedReview.review"
					name="description"
					:placeholder="$t('form.rate-description-placeholder')"
					class="border min-w-full border-gray-300 rounded-md text-sm font-medium text-gray-500 p-3 outline-none resize-none"
					rows="8"
				/>
			</div>
		</template>
		<template #footer>
			<div class="flex p-2 justify-end gap-4 w-full">
				<Button
					variant="outline"
					@click="selectedReview = null"
				>
					{{ $t('form.cancel') }}
				</Button>

				<Button
					:loading="isReviewing"
					@click="submitReview"
				>
					{{ $t('form.review-edit-title') }}
				</Button>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
