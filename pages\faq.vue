<script setup lang="ts">
const { t } = useI18n()

interface FAQ {
	title: string
	subtitle: string
}

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('faq.title'))
})

const active = ref(0)

useSeoMeta({
	title: () => `${t('faq.meta-title')} | ${t('header.meta-site-name')}`,
	description: () => t('faq.meta-description'),
	ogTitle: () => `${t('faq.meta-title')} | ${t('header.meta-site-name')}`,
	ogDescription: () => t('faq.meta-description'),
	twitterTitle: () => `${t('faq.meta-title')} | ${t('header.meta-site-name')}`,
	twitterDescription: () => t('faq.meta-description'),
})

const faqList: FAQ[] = Array.from({ length: 25 }, (_, index) => ({
	title: t(`faq.faq-${index}-title`),
	subtitle: t(`faq.faq-${index}-subtitle`),
}))

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)
useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>

	<Card class="my-6">
		<CardHeader class="text-center py-12 gap-6">
			<h1 class="text-2xl font-bold">
				{{ $t('faq.title') }}
			</h1>
			<p class="text-lg text-gray-600">
				{{ $t('faq.sub-title') }}
			</p>
		</CardHeader>

		<CardContent class="py-4 flex-col flex gap-6 pb-12">
			<template
				v-for="(faq, index) in faqList"
				:key="`faq-${index}`"
			>
				<div class="flex w-full flex-col">
					<button
						class="flex p-4 rounded-lg text-start gap-2 justify-between"
						:class="[active === index ?'text-primary-600 font-bold bg-sky-50':'bg-gray-100']"
						@click="active = index"
					>
						<h2 class="text-sm">
							{{ faq.title }}
						</h2>
						<Icon
							name="lucide:chevron-down"
							:class="{ 'rotate-180': active === index }"
							size="20px"
						/>
					</button>

					<div
						class="flex rounded-lg text-sm text-gray-600 transition-all duration-300 ease-in-out"
						:class="[active === index ?'p-4':'overflow-hidden max-h-0']"
					>
						<p
							class="m-0 p-0 inline"
							v-html="faq.subtitle"
						/>
					</div>
				</div>
			</template>
		</CardContent>
	</Card>
</template>
