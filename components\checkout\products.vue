<script setup lang="ts">
import type { CheckoutOrder, OrderItems } from '~/interfaces/checkout/order'

const { priceFormat } = useCurrency()
const { order } = defineProps<{
	loading?: boolean
	order?: CheckoutOrder
}>()

const products = computed<OrderItems[]>(() => order?.orderItems as OrderItems[])
const totalQuantity = computed<number>(() => products.value.map(i => i.quantity).reduce((a, b) => a + b, 0))
</script>

<template>
	<Card class="shadow-2xl">
		<CardHeader>
			<template v-if="loading">
				<Skeleton class="w-1/2 h-5" />
			</template>
			<template v-else>
				<span class="text-lg font-bold">
					{{ $t('orders.details') }}
				</span>
			</template>
		</CardHeader>
		<CardContent>
			<div class="flex flex-col gap-2 w-full">
				<template v-if="loading">
					<div
						v-for="(_, index) in Array(4)"
						:key="index"
						class="flex w-full gap-2"
					>
						<Skeleton class="w-1/3 h-24" />
						<div class="flex flex-col gap-2 flex-grow">
							<Skeleton class="w-full h-8" />
							<Skeleton class="w-1/2 h-8" />
						</div>
					</div>
				</template>
				<template
					v-for="product in products"
					v-else
					:key="product.productId"
				>
					<div class="flex rounded-lg border w-full p-px gap-2 h-24">
						<div class="flex w-28 h-full bg-gray-100 rounded-s-lg p-2 justify-center">
							<NuxtImg
								class="object-contain w-full h-full"
								provider="backend"
								format="webp"
								:src="product.product?.media?.cover?.[0]?.src"
							/>
						</div>

						<div class="flex flex-col gap-2 w-3/4 pe-2 py-2">
							<span class="text-sm font-normal truncate-2-line h-10">
								{{ product.product.name }}
							</span>

							<div class="flex gap-2 items-end font-bold text-lg">
								<span class=" text-green-700">
									{{ priceFormat(product.price?.value) }}
								</span>
								<span
									dir="ltr"
									class="text-gray-400"
								>
									<span class="text-2xl font-normal">{{ product?.quantity }}</span> X
								</span>
							</div>
						</div>
					</div>
				</template>

				<div class="flex w-full border-dashed border border-gray-100 mt-4 mb-2" />

				<template v-if="loading">
					<Skeleton class="w-full h-8" />
					<Skeleton class="w-full h-8" />
					<Skeleton class="w-full h-8" />
					<Skeleton class="w-full h-8" />
				</template>
				<template v-else>
					<div class="grid grid-cols-2 w-full gap-2 text-base font-semibold text-gray-500">
						<span>
							{{
								$t('cart.payment-sub-amount-title', {
									item: $t('cart.item', { count: totalQuantity }),
									number: totalQuantity,
								})
							}}
						</span>

						<span class="text-end">
							{{ priceFormat(order.subTotal.value) }}
						</span>

						<span class="col-span-2 mb-4">
							{{ $t('cart.payment-tax-inclusive') }}
						</span>

						<span>
							{{ $t('form.shipping') }}
						</span>

						<span class="text-end">
							{{ priceFormat(order.shippingPrice?.value) }}
						</span>

						<span class="text-xs font-normal col-span-2">
							{{ $t('checkout.shipping-delay-hint') }}
						</span>
					</div>
				</template>

				<div class="flex w-full border-dashed border border-gray-100 mt-4 mb-2" />

				<template v-if="loading">
					<Skeleton class="w-full h-6" />
					<Skeleton class="w-full h-4" />
				</template>
				<template v-else>
					<div class="grid grid-cols-2 w-full gap-2 text-base font-semibold ">
						<span class="font-bold text-base text-gray-700">
							{{ $t('cart.total-title') }}
						</span>

						<span class="text-end">
							--------
						</span>

						<span class="col-span-2 mb-4 text-sm text-gray-500">
							{{ $t('form.calculate-total-hint') }}
						</span>
					</div>
				</template>
			</div>
		</CardContent>
	</Card>
</template>
