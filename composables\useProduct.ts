import type { Details, Stock, ProductAttribute, UseProductDetails } from '~/interfaces/product/details'

export const useProduct = (product: Details = null): UseProductDetails => {
	const { priceFormat } = useCurrency()
	const stock = computed<Stock>(() => product?.variance?.stock)

	const hasStock = computed<boolean>(() => !!stock.value)

	const hasOffer = computed<boolean>(() => stock.value?.unPublishedAt && import.meta.client)

	/** Discount Amount **/
	const discountAmount = computed<number>(() => {
		if (!stock.value?.isOffer) {
			return 0
		}

		return Math.abs(stock.value.priceBeforeOffer?.value - stock.value.price.value)
	})

	/** Discount Percent **/
	const discountPercent = computed(() => {
		if (!stock.value?.priceBeforeOffer || stock.value?.priceBeforeOffer?.value === 0) {
			return 0
		}

		return ((discountAmount.value / stock.value.priceBeforeOffer.value) * 100).toFixed(0)
	})

	const discountAmountFormatted = computed(() => priceFormat(discountAmount.value))

	const price = computed(() => {
		return stock.value?.price?.value
	})

	const priceFormatted = computed(() => {
		return priceFormat(price.value)
	})

	const offerPrice = computed(() => {
		return stock.value?.priceBeforeOffer?.value
	})

	const offerPriceFormatted = computed(() => {
		return priceFormat(offerPrice.value)
	})

	const isAvailable = computed(() => stock.value?.quantity > 0)
	const sku = computed(() => product?.variance?.SKU)

	const allAttributes = computed(() => [...(product?.variance?.attributes || []), ...(product?.attributes || [])] as ProductAttribute[])
	return {
		...product,
		hasStock,
		stock,
		hasOffer,
		discountAmount,
		discountPercent,
		price,
		priceFormatted,
		offerPrice,
		offerPriceFormatted,
		discountAmountFormatted,
		brand: product?.brand,
		brandLogo: product?.brand?.media?.logo?.[0]?.src ?? '',
		isAvailable,
		sku,
		allAttributes,
	}
}
