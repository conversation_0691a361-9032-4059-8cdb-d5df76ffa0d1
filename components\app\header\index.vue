<script setup lang="ts">
import { useEventListener } from '@vueuse/core'

const route = useRoute()
const router = useRouter()

const lastScrollY = ref<number>(0)
const hasScroll = computed<boolean>(() => lastScrollY.value > 0)
const hasScrollDown = ref<boolean>(false)

/** Handle on scroll hide the top header */
const handleScroll = (): void => {
	hasScrollDown.value = lastScrollY.value < window.scrollY
	nextTick(() => lastScrollY.value = window.scrollY)
}

/** Event listener **/
useEventListener('scroll', handleScroll)

/** Handle on set wish list drawer */
const setDrawerPage = (drawer: string): void => {
	router.push({
		path: route.path,
		query: {
			...route.query,
			drawer,
		},
	})
}
</script>

<template>
	<header
		:class="{ '-translate-y-8': hasScrollDown, 'has-scrolling': hasScroll }"
		class="flex w-full flex-col transition-transform duration-100 ease-in-out justify-center items-center shadow-lg pb-3 bg-white z-10 sm:pb-0 sticky top-0 max-sm:!pb-0"
	>
		<AppHeaderTop :class="{ '-translate-y-8': hasScrollDown }" />
		<AppHeaderActions
			:has-scroll="lastScrollY>0"
			@open:drawer="setDrawerPage"
		/>
		<AppHeaderMobile @open:drawer="setDrawerPage" />
		<AppHeaderLinksMenu
			class="menu-list"
			:class="{ 'h-visible': !hasScroll }"
		/>
	</header>
</template>

<style lang="scss" scoped>
.menu-list {
  will-change: max-height;
  max-height: 0;
  overflow: hidden;
  padding: 0;
  @apply -z-10;
  &.h-visible {
    @apply transition-all pb-2;
    max-height: 48px;
  }
}

.has-scrolling {
  @apply sm:bg-white/80 backdrop-blur-md;

  // mobile implementations
  @apply max-sm:-translate-y-[100px] pb-0 max-sm:bg-opacity-75;

  :deep(.app-logo) {
    @apply max-h-10 transition-all;
  }
}
</style>
