<script setup lang="ts">
import { useFileDialog } from '@vueuse/core'
import { toast } from 'vue-sonner'
import type { User } from '~/interfaces/auth/auth'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()
const { user } = defineProps<{ user?: User }>()

const fullName = computed(() => user?.firstName + ' ' + user?.lastName)
const firstChar = computed(() => (user?.firstName?.charAt(0) + '.' + user.lastName?.charAt(0))?.toUpperCase())
const phone = computed(() => `+${user?.phone?.code}${user?.phone?.number}`)
const avatar = computed(() => user?.media?.avatar?.src)
const route = useRoute()
const page = ref('profile')
const { t } = useI18n()

const { open, onChange } = useFileDialog({
	accept: 'image/*',
})

interface Link {
	title?: string
	icon?: string
	page?: string
}

const links: Link[] = [
	{
		title: 'profile.link-profile-title',
		icon: 'ui:profile',
		page: 'profile',
	},
	{
		title: 'profile.link-wish-title',
		icon: 'ui:heart',
		page: 'wishlist',
	},
	{
		title: 'profile.link-address-title',
		icon: 'ui:address',
		page: 'address',
	},
	{
		title: 'profile.link-reviews-title',
		icon: 'ui:reviews',
		page: 'reviews',
	},
	{
		title: 'profile.link-orders-title',
		icon: 'ui:orders',
		page: 'orders',
	},
	{
		title: 'profile.link-wallet-title',
		icon: 'ui:wallets',
		page: 'wallet',
	},
]

watch(() => route.query, (query) => {
	page.value = query.page as string || 'profile'
}, { immediate: true })

const isRtl = computed(() => useI18n().locale.value === 'ar')

/** Logout and navigate to home page **/
const logout = async (): Promise<void> => {
	await authStore.logout()
}

const activeParent = computed(() => route.meta?.parent)

onChange(async (files) => {
	if (!files?.[0]) return
	const formData = new FormData()
	formData.append('file', files?.[0])
	const { $api } = useNuxtApp()
	return $api<unknown>('/upload-media', {
		method: 'POST',
		body: formData,
	})
		.then(async (data) => {
			await authStore.uploadAvatar(data)
			toast.success(t('form.profile-image-updated'))
		})
		.catch((error) => {
			console.log('Error on updating user image', error)
		})
})
</script>

<template>
	<Card>
		<CardHeader class="profile-head">
			<div class="flex flex-col gap-1 max-h-96 h-full justify-center items-center">
				<div class="relative flex flex-col">
					<div
						role="button"
						class="flex flex-col rounded-full w-20 h-20 bg-white border-2 border-primary-600 justify-center items-center"
						@click="() => open()"
					>
						<template v-if="avatar">
							<NuxtImg
								:src="avatar"
								provider="backend"
								class="rounded-full w-full h-full object-cover"
							/>
						</template>
						<template v-else>
							<span class="text-xl font-bold">{{ firstChar }}</span>
						</template>
					</div>
					<div
						class="absolute bottom-2 justify-center items-center z-10 bg-white flex rounded-full w-4 h-4 border-primary-600 border overflow-hidden"
					>
						<Icon
							name="lucide:pencil"
							size="8px"
							class="text-primary-600"
						/>
					</div>
				</div>

				<span class="text-lg font-bold truncate-1-line leading-tight">{{ fullName }}</span>
				<span class="text-base font-normal truncate-1-line leading-tight">{{ user?.email }}</span>
				<span
					dir="ltr"
					class="text-base font-normal truncate-1-line leading-tight"
				>
					{{ phone }}
				</span>
			</div>
		</CardHeader>

		<CardContent class="p-0">
			<div class="flex flex-col">
				<template
					v-for="link in links"
					:key="link.title"
				>
					<NuxtLinkLocale
						class="flex w-full justify-between items-center gap-4 border-b border-gray-200 p-4"
						:to="`/my/${link.page}`"
						active-class="bg-primary-600 text-white"
						:class="{ 'bg-primary-600 text-white': activeParent === link.page }"
					>
						<div class="flex gap-4 items-center">
							<Icon
								:name="link.icon"
								class="h-6 w-7"
							/>
							<span class="text-base font-semibold">{{ $t(link.title) }}</span>
						</div>

						<Icon
							name="lucide:chevron-right"
							:class="{ 'rotate-180': isRtl }"
						/>
					</NuxtLinkLocale>
				</template>

				<div
					role="button"
					class="flex w-full justify-between items-center gap-4 border-b border-gray-200 p-4"
					@click.once.prevent="logout"
				>
					<div class="flex gap-4 items-center">
						<span
							class="text-base font-semibold text-gray-500"
						>
							{{ $t('profile.sign-out-title') }}
						</span>
					</div>
				</div>
			</div>
		</CardContent>
	</Card>
</template>

<style scoped lang="scss">
.profile-head {
  background: url('/assets/images/profile-head-bg.png') repeat center center;
  background-size: 120%;
}
</style>
