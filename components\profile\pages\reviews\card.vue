<script setup lang="ts">
import type { Items } from '~/interfaces/auth/rating'

const { review, loading } = defineProps<{
	review?: Items
	loading?: boolean
}>()

const emit = defineEmits<{
	(event: 'edit:review', ratingId: Items): void
}>()

const datetime = computed(() => useDateFormat(review?.createdAt, 'DD-MM-YYYY hh:mm A'))
const image = computed(() => review?.product?.media?.cover?.[0]?.src)
const isPending = computed(() => review.status === 'UnPublished')
</script>

<template>
	<div class="flex w-full flex-col gap-2 border-b border-gray-200 pb-4 max-sm:gap-0">
		<div class="flex w-full rounded-lg border border-gray-200 overflow-hidden max-sm:rounded-b-none">
			<div class="flex w-52 h-44 bg-gray-100 max-sm:hidden">
				<template v-if="loading">
					<Skeleton class="w-full h-full" />
				</template>
				<template v-else-if="review">
					<NuxtImg
						:src="image"
						class="w-full h-full p-2 object-contain"
						provider="backend"
					/>
				</template>
			</div>

			<div class="flex flex-col flex-grow p-4 gap-2 justify-between">
				<template v-if="loading">
					<div class="flex justify-between gap-4">
						<Skeleton class="w-1/2 h-8" />
						<Skeleton class="w-1/3 h-8" />
					</div>
					<Skeleton class="w-2/3 h-8" />
					<div class="flex justify-between gap-4">
						<Skeleton class="w-1/2 h-8" />
						<Skeleton class="w-1/3 h-8" />
					</div>
				</template>
				<template v-else-if="review">
					<div class="flex items-center gap-2 justify-between">
						<span class="text-base truncate-2-line">{{ review.product?.name }}</span>
						<Badge
							v-if="isPending"
							class="!bg-orange-400 px-4 text-white"
						>
							{{ $t('form.pending') }}
						</Badge>
					</div>

					<div class="flex gap-2 items-center">
						<span class="text-lg">{{ $t('form.rate_date') }}</span>
						<span
							class="text-lg px-2 font-semibold"
							dir="ltr"
						>{{ datetime }}</span>
					</div>

					<div class="flex gap-2 items-center justify-between">
						<div class="flex gap-3 items-center">
							<span class="text-lg">{{ $t('form.rates') }}</span>
							<div class="flex gap-1 items-center">
								<Icon
									v-for="i in 5"
									:key="`rate-star-${i}`"
									name="ui:rate-star"
									class="w-5 h-5 "
									:class="review.rating>=i ? 'text-rating-200' : 'text-rating-100'"
								/>
							</div>
						</div>
						<Button
							v-if="isPending"
							variant="text"
							@click.prevent="emit('edit:review', review)"
						>
							<Icon name="lucide:pencil" />
							<span class="font-bold">{{ $t('form.edit') }}</span>
						</Button>
					</div>
				</template>
			</div>
		</div>
		<div class="flex w-full rounded-lg border border-gray-200 p-2 bg-gray-100 max-sm:rounded-t-none max-sm:border-t-none">
			<template v-if="loading">
				<Skeleton class="w-full h-6" />
			</template>
			<template v-else-if="review">
				<p class="text-base whitespace-pre-line">
					{{ review.review }}
				</p>
			</template>
		</div>
	</div>
</template>

<style scoped lang="scss">

</style>
