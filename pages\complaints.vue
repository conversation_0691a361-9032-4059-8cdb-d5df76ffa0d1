<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type { AuthPhone } from '~/interfaces/auth/form'
import type FormPhoneValue from '~/interfaces/form'

const { t } = useI18n()

const key = ref(1)

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('complain.title'))
})

const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		fullName: z.string().min(1, t('error.required')).nullish(),
		email: z.string().email(t('error.email')).nullish(),
		message: z.string().min(1, t('error.required')).nullish(),
		problemType: z.string(),
		phone: z.object({
			number: z.string().min(5, t('error.required')),
			iso: z.string().min(1, t('error.required')),
			code: z.string().min(1, t('error.required')),
			isValid: z.boolean(),
		})
			.refine((phone) => {
				if (!phone.isValid) {
					return Form.setErrors({
						'phone.number': t('error.phone-number-invalid'),
					})
				}

				return true
			}, {
				message: t('error.phone-number-invalid'),
				path: ['number'],
			}),
	})),

	initialValues: {
		fullName: '',
		email: '',
		phone: {
			number: '',
			code: '',
			iso: '',
			isValid: false,
		},
		problemType: 'complaint',
		message: '',
	},
})

const onPhoneUpdate = (value: FormPhoneValue) => {
	Form.setFieldValue('phone', {
		number: value.nationalNumber,
		code: value.countryCallingCode,
		iso: value.countryCode,
		isValid: value.isValid,
	})
}

const onSubmit = Form.handleSubmit((values: AuthPhone): Promise<void> => {
	const { $api } = useNuxtApp()
	return $api<never>('/contact-us', {
		method: 'POST',
		body: values,
	}).then(() => {
		toast.success(t('form.contact-saved-successfully'))
		Form.resetForm()
		key.value += 1
	}).catch((err) => {
		console.log(err)
	})
})

useSeoMeta({
	title: () => `${t('complain.meta-title')} | ${t('header.meta-site-name')}`,
	description: () => t('complain.meta-description'),
	ogTitle: () => `${t('complain.meta-title')} | ${t('header.meta-site-name')}`,
	ogDescription: () => t('complain.meta-description'),
	twitterTitle: () => `${t('complain.meta-title')} | ${t('header.meta-site-name')}`,
	twitterDescription: () => t('complain.meta-description'),
})

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)
useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>

	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="text-center justify-center gap-4 rounded-lg">
			<h1 class="font-bold text-2xl">
				{{ $t('complain.title') }}
			</h1>
			<h2 class="text-lg text-gray-600">
				{{ $t('complain.sub-title') }}
			</h2>
		</CardHeader>
		<CardContent class="grid grid-cols-2 gap-6 py-12 max-md:grid-cols-1">
			<form
				:key="`form-${key}`"
				class="flex col-span-1"
			>
				<div class="flex flex-col gap-4 w-full rounded-lg border shadow p-4 justify-center">
					<span class="whitespace-pre-wrap text-center">
						{{ $t('complain.form-title') }}
					</span>

					<FormField
						v-slot="{ componentField }"
						name="fullName"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-bold">
								{{ $t('form.full-name') }}*
							</FormLabel>
							<FormControl>
								<Input
									type="text"
									:placeholder="$t('form.full-name')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<FormField
						v-slot="{ componentField }"
						name="email"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-bold">
								{{ $t('form.email') }}*
							</FormLabel>
							<FormControl>
								<Input
									type="email"
									:placeholder="$t('form.email')"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<FormPhone
						:error="Form.errors?.value?.phone"
						@update="onPhoneUpdate"
					/>

					<FormField
						v-slot="{ componentField }"
						name="message"
					>
						<FormItem class="w-full relative ">
							<FormLabel class="font-bold">
								{{ $t('form.message') }}*
							</FormLabel>
							<FormControl>
								<textarea
									class="flex border w-full min-h-20 p-2 rounded outline-none text-sm"
									:placeholder="$t('form.message')"
									rows="7"
									v-bind="componentField"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					</FormField>

					<Button @click="onSubmit">
						<span>{{ $t('form.send-message') }}</span>
					</Button>
				</div>
			</form>

			<div class="flex col-span-1">
				<div class="flex justify-between items-center gap-4 w-full max-sm:flex-col">
					<NuxtImg
						src="images/complaints.png"
						class="w-full object-contain object-center p-12"
					/>
				</div>
			</div>
		</CardContent>
	</Card>
</template>
