<script setup lang="ts">
import { toast } from 'vue-sonner'
import type { Cover, Details, RedirectResponse, UseProductDetails } from '~/interfaces/product/details'
import { useCompareStore } from '~/store/useCompareStore'

interface PyNowResponse {
	orderId?: number
}

// useRouteCache((helper) => {
// 	helper.setMaxAge(60).setCacheable().addTags(['product'])
// })

// useCDNHeaders((helper) => {
// 	helper
// 		.addTags(['product'])
// 		.public()
// 		.setNumeric('maxAge', 60) // 1min
// 		.setNumeric('staleIfError', 43200)
// })
const config = useRuntimeConfig()
const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const localePath = useLocalePath()
const slug = computed(() => (route.params?.slug as string[]).join('/'))
const activeSlug = computed(() => route.params?.slug?.[0])
const dir = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
const { data, error, status } = await useApi<Details>(`products/${slug.value}`)

const response = computed<Details>(() => data.value as Details)

const product = ref<UseProductDetails>(useProduct(response.value as Details))

watch(response, (newProduct: Details | null) => {
	if (newProduct) {
		product.value = useProduct(newProduct as Details) as UseProductDetails
	}
}, { deep: true, immediate: true })

const loading = computed(() => status.value !== 'success')
const selectedProduct = ref(null)
const isPaying = ref(false)

/**
 * Redirect to correct product slug
 */
const redirectToCorrectProduct = () => {
	if ('details' in response.value) {
		navigateTo(`/product/${(response.value as RedirectResponse).details.redirectUrl}`, { redirectCode: 301 })
	}
}

/**
 * Redirect to error page
 */
const redirectToErrorPage = () => {
	throw createError({
		statusCode: 404,
	})
}

if ((response.value as RedirectResponse)?.code === 301) {
	redirectToCorrectProduct()
}

if (response.value?.code === 404) {
	redirectToErrorPage()
}

if (error.value) {
	console.error(`Error: in fetching product details`, error.value)
	if (error.value?.statusCode === 404) {
		redirectToErrorPage()
	}
}

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(product.value?.name)
})
/** check the scrolling for details **/
useScrollTo()

/**
 * On Select the variants
 * @param selected
 */
const onSelectVariant = async ({ varianceSlug }) => {
	const productSlug = Array.isArray(route.params.slug) ? route.params.slug[0] : route.params.slug
	return nextTick(() => {
		router.push({
			name: route.name,
			params: {
				...route.params,
				slug: [
					productSlug,
					varianceSlug,
				],
			},
		})
	})
}

/**
 * Handle on create new order
 */
const onPayNow = async (quantity: number) => {
	const { $api } = useNuxtApp()
	isPaying.value = true
	return $api<PyNowResponse>('/orders/buy-now', {
		method: 'POST',
		body: {
			quantity,
			productId: product.value?.productId,
			varianceId: product.value?.variance?.varianceId,
		},
	})
		.then(async (data) => {
			await navigateTo(localePath(`/checkout/${data.orderId}/1`))
		})
		.catch((error) => {
			console.error(error)
			toast.error(error?.message || 'Something went wrong')
		})
		.finally(() => {
			nextTick(() => isPaying.value = false)
		})
}
const compareStore = useCompareStore()

/**
 * Handle on Compare product
 */
const onCompare = async (varianceId: number) => {
	if (!compareStore.products.includes(varianceId)) {
		return compareStore.setProduct(varianceId)
	}

	return compareStore.removeProduct(varianceId)
}

const img = useImage()
const gallery = computed(() => product.value?.variance?.media?.gallery || product.value?.media?.gallery as Cover[])
const metaImage = computed(() => {
	return img(gallery.value?.[0]?.src, {}, { provider: 'backend' })
})

const hasVariant = computed(() => {
	return route.params?.slug?.length > 1
})

definePageMeta({
	title: 'Product',
})

const metaTitle = computed(() => {
	if (!hasVariant.value) {
		return `${product.value?.metaTitle} | ${t('header.meta-site-name')}`
	}

	return `${product.value?.variance?.metaTitle || ''} ${product.value?.metaTitle || ''}  | ${t('header.meta-site-name')}`
})

const metaDescription = computed(() => {
	if (!hasVariant.value) {
		return `${product.value?.metaDescription}`
	}

	return `${product.value?.variance?.metaDescription || ''} ${product.value?.metaTitle || ''}`
})

useSeoMeta({
	title: metaTitle,
	description: metaDescription,
	ogTitle: metaTitle,
	ogDescription: metaDescription,
	twitterTitle: metaTitle,
	twitterDescription: metaDescription,
	ogImage: metaImage,
	twitterImage: metaImage,
})

const canonical = computed(() => {
	return `${config.public.siteUrl}${localePath({ name: route.name, params: { slug: activeSlug.value } })}`
})
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)

useHead({
	link: [
		{ rel: 'canonical', href: canonical },
		{ rel: 'twitter:card', href: siteUrl },
		{ rel: 'og:url', href: siteUrl },
	],
})

const varianceId = computed(() => product.value?.variance?.varianceId ?? (new Date()).getTime())
</script>

<template>
	<Card
		:key="varianceId"
		class="-mt-2 pt-2"
		:data-variance-id="varianceId"
	>
		<Breadcrumb
			:links="breadCrumbLinks"
			:loading="loading"
			class="!border-0 !shadow-none"
		/>

		<CardContent>
			<div
				class="flex gap-4 py-2 max-sm:flex-col"
			>
				<!-- Gallery -->
				<ProductGallery
					:product="product as UseProductDetails"
					:loading="loading"
				/>

				<div class="flex gap-4 max-md:flex-col w-full max-md:w-1/2 max-sm:w-full">
					<!-- Description -->

					<ProductDetails
						:product="product as UseProductDetails"
						:loading="loading"
						:has-variant="hasVariant"
						@select:variant="onSelectVariant"
					/>

					<!-- Sticky Payment Section on Mobile view -->
					<ProductPaymentCardMobile
						:product="product as UseProductDetails"
						:loading="loading"
						:is-paying="isPaying"
						@confirm:add-to-cart="selectedProduct = $event"
						@product:pay-now="onPayNow"
						@product:compare="onCompare"
					/>

					<!-- Payment Section -->
					<ProductPaymentCard
						:product="product as UseProductDetails"
						:loading="loading"
						:is-paying="isPaying"
						@confirm:add-to-cart="selectedProduct = $event"
						@product:pay-now="onPayNow"
						@product:compare="onCompare"
					/>
				</div>
			</div>
		</CardContent>
	</Card>

	<template v-if="product?.productId">
		<ProductSuggestions
			:product-id="product?.productId"
		/>
		<ProductInfo
			:loading="loading"
			:product="product as UseProductDetails"
		/>

		<Modal
			v-if="!!selectedProduct"
			:title="$t('cart-list.add-item-title')"
			@close="selectedProduct = null"
		>
			<template #footer>
				<div class="flex w-full justify-end items-center gap-4">
					<Button
						variant="outline"
						@click.once="selectedProduct = null"
					>
						<span>{{ $t('cart-list.confirm-continue') }}</span>
					</Button>

					<Button as-child>
						<NuxtLinkLocale to="/cart">
							<span>{{ $t('cart-list.confirm-continue-pay') }}</span>
						</NuxtLinkLocale>
					</Button>
				</div>
			</template>

			<template #body>
				<div class="flex w-full px-4 justify-center items-center mb-4">
					<DrawerCartListCard
						:product="selectedProduct"
						:view-only="true"
					/>
				</div>
			</template>
		</Modal>
	</template>
</template>

<style  scoped>
:deep(.description [dir="auto"]) {
  direction: v-bind(dir) !important;
}
</style>
