<script setup lang="ts">
const { t } = useI18n()

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('stores.title'))
})

const list = ref([
	{
		title: 'الدوار السابع',
		text: 'صالح الصمادي السابع, عمّان',
		time: 'السبت - الخميس: 9:00 ص - 11:00 م',
		path: 'https://maps.app.goo.gl/Qnbq3MiwF568SthQA',
	},

	{
		title: 'ستي مول',
		text: 'City Mall, King Abdullah II St 295, Amman',
		time: 'السبت - الخميس: 9:00 ص - 11:00 م',
		path: 'https://maps.app.goo.gl/nykVFQZ5JHq6rik96',
	},
])

useSeoMeta({
	title: () => `${t('stores.meta-title')}`,
	description: () => t('stores.meta-description'),
	ogTitle: () => `${t('stores.meta-title')}`,
	ogDescription: () => t('stores.meta-description'),
	twitterTitle: () => `${t('stores.meta-title')}`,
	twitterDescription: () => t('stores.meta-description'),
})

const route = useRoute()
const config = useRuntimeConfig()
const siteUrl = computed(() => `${config.public.siteUrl}${route.path}`)
useHead({
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': siteUrl.value,
				'url': siteUrl.value,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					siteUrl.value,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>
	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="text-center justify-center gap-4 text-white bg-black md:h-[500px] rounded-lg">
			<h1 class="font-bold text-2xl">
				{{ $t('stores.title') }}
			</h1>
			<h2 class="font-bold text-xl">
				{{ $t('stores.sub-title') }}
			</h2>
		</CardHeader>

		<CardContent class="py-12">
			<div class="flex flex-col shadow rounded-lg p-4 border max-w-sm w-full max-md:max-w-full">
				<template
					v-for="(item, index) in list"
					:key="index"
				>
					<div class="flex p-2 flex-col gap-2 my-2">
						<span class="text-lg font-semibold">{{ item.title }}</span>
						<span class="text-base text-gray-600 font-semibold">{{ item.text }}</span>
						<div class="flex items-center gap-2">
							<Icon
								name="ui:time"
								size="15px"
							/>
							<span>{{ item.time }}</span>
						</div>
						<div class="flex">
							<Button
								:as-child="true"
								:size="'sm'"
							>
								<a
									:href="item.path"
									target="_blank"
								>
									{{ $t('stores.direction') }}
								</a>
							</Button>
						</div>
					</div>
					<div
						v-if="index !== list.length - 1"
						class="flex w-full border-b h-px"
					/>
				</template>
			</div>
		</CardContent>
	</Card>
</template>
