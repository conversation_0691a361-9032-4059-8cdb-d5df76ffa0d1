<script setup lang="ts">
import { useCurrency } from '~/composables/useCurrency'
import { useCartStore } from '~/store/useCartStore'
import type { CompareResponse, Variances, Attributes, AttributeOptions } from '~/interfaces/compare/compare'
import { useCompareStore } from '~/store/useCompareStore'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const localePath = useLocalePath()
const cartStore = useCartStore()
const compareStore = useCompareStore()
const { priceFormat } = useCurrency()

const ids = computed(() => route.params?.ids)
const cartItemsVarianceIds = computed(() => cartStore.list.map(item => item?.varianceId))
const isAddingToCart = ref(false)

const { data, error, status } = await useApi<CompareResponse>(`/compare`, {
	query: {
		'varianceIds[]': ids.value || [],
	},
	server: false,
})

if (error.value) {
	console.error(`Error: in fetching product details`, error.value)
}

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('compare.title'))
})

// const isFav = ref<boolean>(false)
const loading = computed<boolean>(() => status.value !== 'success')
const variances = computed(() => (data.value as CompareResponse)?.variances as Variances[])
const attributes = computed(() => (data.value as CompareResponse)?.attributes as Attributes[])

definePageMeta({
	name: 'Compare',
	title: 'Compare',
	ssr: false,
	validate: async (route) => {
		return route.params?.ids?.length >= 1
	},
})

/** Set SEO Data **/
const { setSeoData } = useMetaData({
	pageName: 'Compare',
	fullPath: route?.fullPath,
})
setSeoData()

/**
 * Get Option Value
 * @param options
 * @param id
 */
const getOptionValue = (options: AttributeOptions[], id: number) => {
	if (!options || !id) {
		return '--------'
	}
	return options.find(option => option?.varianceId === id)?.name ?? '--------'
}

/**
 * On Remove item from the compare list
 * @param item
 */
const onRemoveItem = (item: Variances) => {
	compareStore.removeProduct(item.varianceId)

	if (compareStore.isEmpty) {
		return router.push(localePath('/'))
	}

	const list = compareStore.products
	router.push(localePath(`/compare/${list.join('/')}`))
}

/** handle on adding item to cart **/
const onAddingToCart = async (item: Variances) => {
	isAddingToCart.value = true
	await cartStore.addToList({
		productId: item.productId,
		quantity: 1,
		varianceId: item.varianceId,
	})
	isAddingToCart.value = false
}

const removeList = () => {
	compareStore.clearAll()
	if (compareStore.isEmpty) {
		return router.push(localePath('/'))
	}
}
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		:loading="loading"
		class="!border-0 !shadow-none"
	/>

	<Card class="mt-6">
		<CardHeader>
			<template v-if="loading">
				<Skeleton class="w-52 h-6" />
			</template>
			<template v-else>
				<div class="flex w-full items-center justify-between">
					<span class="font-bold text-lg">{{ $t('compare.title') }}</span>
					<Icon
						name="ui:hand-swipe"
						size="25px"
						class="sm:hidden"
					/>
				</div>
			</template>
		</CardHeader>
		<CardContent class="flex flex-col gap-6 max-sm:hidden">
			<template v-if="loading">
				<div class="flex flex-col gap-2">
					<div
						class="grid grid-cols-3 w-full p-2 gap-4"
						:class="`grid-cols-${compareStore?.products?.length +1}`"
					>
						<div class="flex w-full flex-col items-center gap-2" />
						<div
							v-for="(_, index) in Array(compareStore?.products?.length)"
							:key="`${_}-${index}-head`"
							class="flex w-full flex-col items-center gap-2"
						>
							<Skeleton class="w-2/4 h-36 aspect-square" />
							<Skeleton class="w-4/5 h-7" />
							<Skeleton class="w-4/5 h-4" />
						</div>
					</div>
					<div
						v-for="(_, index) in Array(6)"
						:key="`${_}-${index}`"
						class="grid grid-cols-3 w-full p-2 gap-2"
						:class="`grid-cols-${compareStore?.products?.length +1}`"
					>
						<Skeleton
							v-for="(p, j) in Array(compareStore?.products?.length +1)"
							:key="`${p}-${j}-r`"
							class="w-full h-10"
						/>
					</div>
				</div>
			</template>
			<template v-else>
				<!-- Items Rows -->
				<div
					class="grid auto-rows-auto"
					:class="`grid-cols-${1 + ids?.length}`"
				>
					<div
						v-for="(item, index) in variances"
						:key="`${item?.varianceId}-${index}`"
						class="text-center px-4 w-full flex flex-col items-center"
					>
						<div class="flex relative">
							<NuxtImg
								:src="item?.media?.gallery?.[0]?.src ?? 'images/no-image.png'"
								:alt="item.varianceName"
								:title="item.varianceName"
								class="w-32 h-32 object-contain mx-auto"
								width="170"
								height="170"
								:provider="item?.media?.gallery?.[0]?.src ? 'backend' : ''"
							/>
							<button
								class="absolute start-0 top-0 bg-gray-100 rounded p-1 flex items-center justify-center"
								@click="onRemoveItem(item)"
							>
								<Icon
									name="lucide:trash-2"
									size="15px"
									class="text-gray-400"
								/>
							</button>
						</div>
						<h3 class="text-primary-500 font-bold w-2/3 line-clamp-2">
							<NuxtLinkLocale
								:title="`${item?.name} ${item?.varianceName}`"
								:to="`/product/${item.slug}`"
							>
								{{ item?.name }}, {{ item?.varianceName }}
							</NuxtLinkLocale>
						</h3>
						<span class="font-bold text-green-1000">
							{{ priceFormat(item?.stock?.price?.value) }}
						</span>
					</div>
				</div>

				<!-- Attributes and Variance Rows -->

				<div class="flex gap-0 w-full flex-col">
					<div
						v-for="(attr, index) in attributes"
						:key="`attributes-${index}`"
						class="grid auto-rows-auto w-full p-2"
						:class="[`grid-cols-${1 + ids?.length}`, index % 2 === 0 &&'bg-[#F3F9FC]']"
					>
						<div class="flex items-center font-medium text-gray-700 gap-2">
							<Icon
								:name="`ui:${attr.icon ?? 'colors'}`"
								size="25px"
							/>
							<span class="text-lg font-bold">
								{{ attr.attribute }}
							</span>
						</div>

						<!-- Attribute values for each variance -->
						<div
							v-for="(variant, vIndex) in variances"
							:key="`key${vIndex}-${variant?.varianceId}`"
							class="text-center py-2"
						>
							{{ getOptionValue(attr?.attributeOptions, variant.varianceId) }}
						</div>
					</div>
				</div>
				<!-- Add to Cart Row -->
				<div
					class="grid auto-rows-auto gap-2"
					:class="`grid-cols-${1 + ids?.length}`"
				>
					<div />
					<div
						v-for="(item, iItem) in variances"
						:key="`item-variance-${iItem}`"
						class="text-center"
					>
						<Button
							v-if="!cartItemsVarianceIds.includes(item.varianceId)"
							:size="'sm'"
							:loading="isAddingToCart"
							@click="onAddingToCart(item)"
						>
							{{ $t('wish-list.add-to-cart') }}
						</Button>
					</div>
				</div>
			</template>
		</CardContent>
		<CardContent class="overflow-x-auto !px-0 hidden max-sm:flex">
			<table class="table-fixed">
				<thead>
					<tr class="z-0">
						<th class="sticky start-0 bg-white border-e min-h-full min-w-44 h-52 z-0">
							<div class="flex flex-col bg-white  justify-evenly gap-6 items-start min-h-full flex-1 flex-grow h-32">
								<NuxtLinkLocale
									to="/"
									class="text-primary-600 font-normal px-4"
									@click.prevent="removeList"
								>
									{{ $t('form.remove-compare-products') }}
								</NuxtLinkLocale>
								<div class="flex gap-2 items-center bg-[#F3F9FC] w-full p-4">
									<Icon
										name="ui:brand"
										size="20px"
									/>
									<span class="text-sm font-bold">
										{{ $t('orders.product') }}
									</span>
								</div>
							</div>
						</th>
						<th
							v-for="(item, index) in variances"
							:key="`${item?.varianceId}-${index}-mobile`"
							class="w-full min-w-44 -z-20 justify-center items-center px-2"
						>
							<div class="flex -z-20">
								<NuxtImg
									:src="item?.media?.gallery?.[0]?.src"
									:alt="item.varianceName"
									class="w-32 h-32 object-contain mx-auto"
									width="170"
									height="170"
									provider="backend"
								/>
								<div class="relative pe-2">
									<button
										class="absolute end-2 top-0 bg-gray-100 rounded p-1 flex items-center justify-center"
										@click="onRemoveItem(item)"
									>
										<Icon
											name="lucide:trash-2"
											size="15px"
											class="text-gray-400"
										/>
									</button>
								</div>
							</div>
							<NuxtLinkLocale
								class="text-primary-500 font-bold line-clamp-2 text-center flex"
								:to="`/product/${item.slug}`"
							>
								{{ item?.name }}, {{ item?.varianceName }}
							</NuxtLinkLocale>
							<div class="font-bold text-green-1000 py-2">
								{{ priceFormat(item?.stock?.price?.value) }}
							</div>
						</th>
					</tr>
				</thead>
				<tbody>
					<tr
						v-for="(attr, index) in attributes"
						:key="`attributes-${index}-mobile`"
						class="w-full"
						:class="index % 2 === 0 ?'bg-[#F3F9FC]':'bg-white'"
					>
						<td
							class="sticky start-0 border-e p-4 shadow-md"
							:class="index % 2 === 0 ?'bg-[#F3F9FC]':'bg-white'"
						>
							<div class="flex items-center font-medium text-gray-700 gap-2">
								<Icon
									:name="`ui:${attr.icon ?? 'colors'}`"
									size="20px"
								/>
								<span class="text-sm font-bold">
									{{ attr.attribute }}
								</span>
							</div>
						</td>

						<!-- Attribute values for each variance -->
						<td
							v-for="(variant, vIndex) in variances"
							:key="`key${vIndex}-${variant?.varianceId}-mobile`"
							class="p-4 -z-10 text-center"
						>
							<span class="text-center">
								{{ getOptionValue(attr?.attributeOptions, variant.varianceId) }}
							</span>
						</td>
					</tr>
					<!-- Add to Cart Row -->
					<tr class="z-0">
						<td
							class="text-center bg-gray-50 sticky start-0"
						/>
						<td
							v-for="(item, iItem) in variances"
							:key="`item-variance-${iItem}-mobile-footer`"
							class="text-center -z-10 bg-gray-50 py-2"
						>
							<Button
								v-if="!cartItemsVarianceIds.includes(item.varianceId)"
								:size="'sm'"
								:loading="isAddingToCart"
								class="z-0"
								@click="onAddingToCart(item)"
							>
								{{ $t('wish-list.add-to-cart') }}
							</Button>
						</td>
					</tr>
				</tbody>
			</table>
		</CardContent>
	</Card>
</template>
