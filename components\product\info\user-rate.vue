<script setup lang="ts">
import type { Review } from '~/interfaces/product/rate'

const { review } = defineProps<{
	review?: Review | undefined
}>()

const { t } = useI18n()
const user = computed(() => review?.user)
const userAvatar = computed(() => user.value?.media?.avatar?.src)
const userName = computed(() => {
	return user.value?.firstName
		? `${user.value?.firstName} ${user.value?.lastName || ''}`
		: t('text.unknown')
})
</script>

<template>
	<div class="flex flex-col gap-2 border-b border-gray-200">
		<div class="flex gap-2">
			<div class="flex rounded-full overflow-hidden bg-primary-200 w-8 h-8 justify-center items-center">
				<template v-if="userAvatar">
					<NuxtImg
						:src="userAvatar"
						class="w-full h-full object-cover drop-shadow-lg"
						provider="backend"
					/>
				</template>
				<template v-else>
					<Icon
						name="ui:user-avatar"
						class="text-primary-600"
					/>
				</template>
			</div>
			<div class="flex flex-col">
				<span class="text-xs text-gray-500 font-medium">{{ userName }}</span>
				<span class="text-xs text-gray-500 font-medium">10/10/2020</span>
			</div>
		</div>
		<div class="flex gap-1 items-center">
			<Icon
				v-for="(_, index) in Array(5)"
				:key="`user-rate-${index}`"
				name="ui:rate-star"
				class="h-3"
				:class="review?.rating >=index?'text-green-500':'text-gray-300'"
			/>
		</div>
		<div
			class="flex text-sm text-gray-800 font-medium py-2 pb-4 min-h-10"
		>
			{{ review.review }}
		</div>
	</div>
</template>
