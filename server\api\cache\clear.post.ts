import Redis from 'ioredis'
// Alternative: import { createRedisStorage } from '~/server/utils/redis'

export default defineEventHandler(async () => {
	try {
		// Use regular Redis connection instead of cluster since AWS ElastiCache is likely a single instance
		const redis = new Redis({
			host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
			port: 6379,
			tls: {
				servername: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
			},
		})

		console.log('Connected to Redis instance')

		const keys: string[] = []

		// Scan the single Redis instance directly
		let cursor = '0'
		do {
			const [nextCursor, foundKeys] = await redis.scan(cursor, 'MATCH', '*', 'COUNT', 500)
			cursor = nextCursor

			if (foundKeys.length > 0) {
				keys.push(...foundKeys)
				// Delete keys one by one to avoid CROSSSLOT error
				for (const key of foundKeys) {
					await redis.del(key)
				}
			}
		} while (cursor !== '0')

		console.log(`Total keys found and deleted: ${keys.length}`)

		// Close the connection
		await redis.quit()

		return {
			success: true,
			message: '<PERSON><PERSON> cleared successfully',
			deletedKeys: keys,
			totalDeleted: keys.length,
		}
	} catch (error) {
		return {
			message: 'Failed to del cache keys',
			error: `${error}`,
		}
	}
})
