<script setup lang="ts">
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '~/components/ui/carousel'
import type { Brand } from '~/interfaces/brands/brand'
import type { Details as ProductDetails } from '~/interfaces/product/details'
import type { ProductList } from '~/interfaces/product/product-list'

const { brand } = defineProps<{
	brand: Brand
}>()

const { data, status, error } = await useApi<ProductList>('products', {
	query: { brandId: brand?.brandId, limit: 10 },
})

const products = computed(() => (data.value as ProductList)?.items as ProductDetails[])

if (error.value) {
	console.error('Error fetching new arrival:', error.value)
}

const loading = computed<boolean>(() => status.value !== 'success')
const brandImage = computed(() => brand?.media?.logoName?.src || '')
const brandName = computed(() => brand?.name)
</script>

<template>
	<div class="col-span-1">
		<div class="flex flex-col gap-4 p-2 bg-[#F8EDF6]">
			<div class="w-full flex justify-between items-center">
				<NuxtImg
					:src="brandImage"
					provider="backend"
					format="webp"
					class="max-h-10 max-sm:max-h-8 object-fill"
					:alt="brandName"
					:title="brandName"
					width="100%"
					height="100%"
					loading="lazy"
				/>
				<Button
					size="sm"
					variant="outline"
					:as-child="true"
				>
					<NuxtLinkLocale :to="`/brands/${brand.slug}`">
						{{ $t('home.see-more') }}
					</NuxtLinkLocale>
				</Button>
			</div>
			<div class="flex w-full gap-4 place-items-stretch">
				<Carousel
					:opts="{ align: 'start', slidesToScroll: 'auto' }"
					class="w-full col-span-4"
				>
					<template #default="{ canScrollNext, canScrollPrev }">
						<CarouselContent>
							<template v-if="loading">
								<CarouselItem
									v-for="(_, index) in Array(10)"
									:key="`card-loading-brands-${index}`"
									class="flex flex-col rounded h-full border p-2 border-rose-100 max-w-36 me-4"
								>
									<Skeleton class="w-full h-28 bg-rose-100 mb-2" />
									<Skeleton class="w-4/5 h-6 bg-rose-100 mb-2" />
									<Skeleton class="w-1/3 h-5 bg-rose-100 mb-1.5" />
								</CarouselItem>
							</template>
							<template v-else>
								<CarouselItem
									v-for="product in products"
									:key="product?.productId"
									class="basis-4/10"
								>
									<ProductCard
										:key="`product-brand-${product?.productId}`"
										:product="product"
										variant="sm"
									/>
								</CarouselItem>
							</template>
						</CarouselContent>
						<CarouselPrevious v-if="!loading && canScrollPrev" />
						<CarouselNext v-if="!loading && canScrollNext" />
					</template>
				</Carousel>
			</div>
		</div>
	</div>
</template>
